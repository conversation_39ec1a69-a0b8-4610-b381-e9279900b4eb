"""
海天AI纳斯达克交易系统 - 主应用入口
基于: 项目手册4.1节MVP版本技术栈配置
创建日期: 2025年7月13日
技术栈: FastAPI 0.116.1 + Python 3.13.2
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
from datetime import datetime

# 创建FastAPI应用实例
app = FastAPI(
    title="海天AI纳斯达克交易系统",
    description="AI驱动的纳斯达克实时量化交易系统API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 根路径健康检查
@app.get("/")
async def root():
    """系统健康检查和基本信息"""
    return {
        "message": "海天AI纳斯达克交易系统",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "environment": os.getenv("ENVIRONMENT", "development"),
        "docs": "/docs",
        "redoc": "/redoc"
    }

# 健康检查端点
@app.get("/health")
async def health_check():
    """详细的健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "api": "running",
            "database": "checking...",  # 后续会添加数据库连接检查
            "ai_traders": "initializing",
            "trading_engine": "standby"
        },
        "version": "1.0.0"
    }

# API版本信息
@app.get("/api/v1/info")
async def api_info():
    """API版本和功能信息"""
    return {
        "api_version": "v1",
        "features": [
            "AI交易员管理",
            "实时行情数据",
            "交易执行引擎",
            "风险控制系统",
            "性能监控"
        ],
        "endpoints": {
            "traders": "/api/v1/traders",
            "trading": "/api/v1/trading",
            "market_data": "/api/v1/market",
            "monitoring": "/api/v1/monitoring"
        }
    }

# 异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "系统内部错误，请稍后重试",
            "timestamp": datetime.now().isoformat()
        }
    )

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    print("🚀 海天AI纳斯达克交易系统启动中...")
    print(f"📊 环境: {os.getenv('ENVIRONMENT', 'development')}")
    print(f"🔗 数据库: {os.getenv('POSTGRES_HOST', 'localhost')}:{os.getenv('POSTGRES_PORT', '5432')}")
    print("✅ 系统启动完成")

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理操作"""
    print("🛑 海天AI纳斯达克交易系统正在关闭...")
    print("✅ 系统已安全关闭")

if __name__ == "__main__":
    # 开发环境直接运行
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("BACKEND_PORT", "8001")),
        reload=True,
        log_level="info"
    )
