# =============================================================================
# 海天AI纳斯达克交易系统 - 数据库连接配置
# 基于: SQLAlchemy 2.0 + AsyncPG + PostgreSQL 17.5
# 创建日期: 2025年7月18日
# =============================================================================

from typing import AsyncGenerator, Generator
import logging
from sqlalchemy import create_engine, event
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlmodel import SQLModel

from app.core.settings import Settings

# 配置日志
logger = logging.getLogger(__name__)

# 全局设置实例
settings = Settings()

# =============================================================================
# 同步数据库引擎配置
# =============================================================================

# 构建同步数据库URL
SYNC_DATABASE_URL = (
    f"postgresql://{settings.database.user}:{settings.database.password}"
    f"@{settings.database.host}:{settings.database.port}/{settings.database.database}"
)

# 创建同步引擎
sync_engine = create_engine(
    SYNC_DATABASE_URL,
    poolclass=QueuePool,
    pool_size=settings.database.pool_size,
    max_overflow=settings.database.max_overflow,
    pool_timeout=settings.database.pool_timeout,
    pool_recycle=settings.database.pool_recycle,
    pool_pre_ping=True,  # 连接前验证
    echo=settings.debug,  # 开发环境显示SQL
    future=True,  # 使用SQLAlchemy 2.0风格
)

# 创建同步会话工厂
SessionLocal = sessionmaker(
    bind=sync_engine,
    class_=Session,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False,
)

# =============================================================================
# 异步数据库引擎配置
# =============================================================================

# 构建异步数据库URL
ASYNC_DATABASE_URL = (
    f"postgresql+asyncpg://{settings.database.user}:{settings.database.password}"
    f"@{settings.database.host}:{settings.database.port}/{settings.database.database}"
)

# 创建异步引擎
async_engine = create_async_engine(
    ASYNC_DATABASE_URL,
    poolclass=QueuePool,
    pool_size=settings.database.pool_size,
    max_overflow=settings.database.max_overflow,
    pool_timeout=settings.database.pool_timeout,
    pool_recycle=settings.database.pool_recycle,
    pool_pre_ping=True,
    echo=settings.debug,
    future=True,
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False,
)

# =============================================================================
# 数据库会话依赖
# =============================================================================

def get_db() -> Generator[Session, None, None]:
    """
    获取同步数据库会话
    用于FastAPI依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取异步数据库会话
    用于异步操作
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"Async database session error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()

# =============================================================================
# 数据库初始化和管理
# =============================================================================

def create_tables():
    """
    创建所有数据库表
    仅在开发环境使用，生产环境使用Alembic迁移
    """
    try:
        SQLModel.metadata.create_all(bind=sync_engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise


async def create_tables_async():
    """
    异步创建所有数据库表
    """
    try:
        async with async_engine.begin() as conn:
            await conn.run_sync(SQLModel.metadata.create_all)
        logger.info("Database tables created successfully (async)")
    except Exception as e:
        logger.error(f"Failed to create database tables (async): {e}")
        raise


def drop_tables():
    """
    删除所有数据库表
    仅在开发环境使用
    """
    try:
        SQLModel.metadata.drop_all(bind=sync_engine)
        logger.info("Database tables dropped successfully")
    except Exception as e:
        logger.error(f"Failed to drop database tables: {e}")
        raise


# =============================================================================
# 数据库连接事件监听
# =============================================================================

@event.listens_for(sync_engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """
    数据库连接事件监听
    可以在这里设置连接级别的配置
    """
    if settings.debug:
        logger.debug("Database connection established")


@event.listens_for(sync_engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """
    连接池检出事件监听
    """
    if settings.debug:
        logger.debug("Database connection checked out from pool")


# =============================================================================
# 数据库健康检查
# =============================================================================

def check_database_health() -> bool:
    """
    检查数据库连接健康状态
    """
    try:
        with sync_engine.connect() as conn:
            conn.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False


async def check_database_health_async() -> bool:
    """
    异步检查数据库连接健康状态
    """
    try:
        async with async_engine.connect() as conn:
            await conn.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Async database health check failed: {e}")
        return False


# =============================================================================
# 数据库信息获取
# =============================================================================

def get_database_info() -> dict:
    """
    获取数据库连接信息
    """
    return {
        "database_url": SYNC_DATABASE_URL.replace(f":{settings.database.password}@", ":***@"),
        "pool_size": settings.database.pool_size,
        "max_overflow": settings.database.max_overflow,
        "pool_timeout": settings.database.pool_timeout,
        "pool_recycle": settings.database.pool_recycle,
        "engine_echo": settings.debug,
    }


# =============================================================================
# 导出接口
# =============================================================================

__all__ = [
    "sync_engine",
    "async_engine", 
    "SessionLocal",
    "AsyncSessionLocal",
    "get_db",
    "get_async_db",
    "create_tables",
    "create_tables_async",
    "drop_tables",
    "check_database_health",
    "check_database_health_async",
    "get_database_info",
]
