"""
Settings and configuration classes for AI Trading System.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any
from pydantic import Field, field_validator, model_validator
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """PostgreSQL数据库配置"""

    host: str = Field(default="localhost", env="POSTGRES_HOST")
    port: int = Field(default=5432, env="POSTGRES_PORT")
    user: str = Field(default="postgres", env="APP_DB_USER")
    password: Optional[str] = Field(default=None, env="APP_DB_PASSWORD")
    database: str = Field(default="ai_trading", env="POSTGRES_DB")
    
    # 连接池配置
    pool_size: int = Field(default=20, env="DB_POOL_SIZE")
    max_overflow: int = Field(default=30, env="DB_MAX_OVERFLOW")
    pool_timeout: int = Field(default=30, env="DB_POOL_TIMEOUT")
    pool_recycle: int = Field(default=3600, env="DB_POOL_RECYCLE")
    
    # SSL配置
    ssl_mode: str = Field(default="prefer", env="DB_SSL_MODE")
    
    @property
    def database_url(self) -> str:
        """构建数据库连接URL"""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"
    
    model_config = {
        "env_prefix": "DB_",
        "extra": "ignore"
    }


class AIModelSettings(BaseSettings):
    """AI模型配置"""
    
    # OpenAI GPT-4配置
    openai_api_key: Optional[str] = Field(default=None, env="AI_MODEL_API_KEY")
    openai_base_url: str = Field(default="https://api.openai.com/v1", env="AI_MODEL_BASE_URL")
    openai_model: str = Field(default="gpt-4", env="AI_MODEL_NAME")
    openai_timeout: int = Field(default=60, env="OPENAI_TIMEOUT")
    
    # Anthropic Claude配置
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    anthropic_base_url: str = Field(default="https://api.anthropic.com", env="ANTHROPIC_BASE_URL")
    anthropic_model: str = Field(default="claude-3-sonnet-20240229", env="ANTHROPIC_MODEL")
    anthropic_timeout: int = Field(default=60, env="ANTHROPIC_TIMEOUT")

    # Google Gemini配置
    google_api_key: Optional[str] = Field(default=None, env="GOOGLE_API_KEY")
    google_model: str = Field(default="gemini-pro", env="GOOGLE_MODEL")
    google_timeout: int = Field(default=60, env="GOOGLE_TIMEOUT")
    
    # 通用配置
    max_concurrent_requests: int = Field(default=10, env="AI_MAX_CONCURRENT")
    request_retry_times: int = Field(default=3, env="AI_RETRY_TIMES")
    request_retry_delay: float = Field(default=1.0, env="AI_RETRY_DELAY")
    
    model_config = {
        "env_prefix": "AI_",
        "extra": "ignore"
    }


class QMTSettings(BaseSettings):
    """QMT量化交易接口配置"""
    
    # QMT连接配置
    host: str = Field(default="localhost", env="QMT_HOST")
    port: int = Field(default=58610, env="QMT_PORT")
    username: Optional[str] = Field(default=None, env="QMT_USERNAME")
    password: Optional[str] = Field(default=None, env="QMT_PASSWORD")

    # 交易配置
    account_id: Optional[str] = Field(default=None, env="QMT_ACCOUNT_ID")
    session_timeout: int = Field(default=300, env="QMT_SESSION_TIMEOUT")
    max_concurrent_orders: int = Field(default=3, env="QMT_MAX_CONCURRENT")
    order_timeout: int = Field(default=10, env="QMT_ORDER_TIMEOUT")
    
    # 数据配置
    data_update_interval: int = Field(default=1, env="QMT_DATA_INTERVAL")
    tick_data_buffer_size: int = Field(default=1000, env="QMT_TICK_BUFFER")
    
    model_config = {
        "env_prefix": "QMT_",
        "extra": "ignore"
    }


class TradingSettings(BaseSettings):
    """交易系统配置"""
    
    # 全局交易参数
    total_capital: float = Field(default=********.0, env="MAX_USABLE_FUNDS")  # 1000万
    max_usable_capital_ratio: float = Field(default=0.8, env="TRADING_MAX_CAPITAL_RATIO")
    max_single_trade_ratio: float = Field(default=0.1, env="SINGLE_TRANSACTION_LIMIT")
    max_single_stock_ratio: float = Field(default=0.15, env="RECOMMENDED_POSITION_RATE")

    # AI交易员数量
    ai_trader_count: int = Field(default=8, env="AI_TRADER_COUNT")
    
    # 风险控制
    daily_loss_limit_ratio: float = Field(default=0.03, env="TRADING_DAILY_LOSS_LIMIT")
    max_drawdown_ratio: float = Field(default=0.08, env="TRADING_MAX_DRAWDOWN")
    
    # 交易时间
    market_open_time: str = Field(default="09:30", env="TRADING_OPEN_TIME")
    market_close_time: str = Field(default="15:00", env="TRADING_CLOSE_TIME")
    daily_review_time: str = Field(default="15:10", env="TRADING_REVIEW_TIME")
    
    # 数据更新频率
    data_snapshot_interval: int = Field(default=1, env="TRADING_SNAPSHOT_INTERVAL")
    
    model_config = {
        "env_prefix": "TRADING_",
        "extra": "ignore"
    }


class SecuritySettings(BaseSettings):
    """安全配置"""
    
    # JWT配置
    secret_key: Optional[str] = Field(default=None, env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # API安全
    api_rate_limit: int = Field(default=100, env="API_RATE_LIMIT")
    api_rate_limit_window: int = Field(default=60, env="API_RATE_WINDOW")
    
    # 数据加密
    encryption_key: Optional[str] = Field(default=None, env="ENCRYPTION_KEY")
    
    # CORS配置
    cors_origins: List[str] = Field(default=["http://localhost:3000"], env="CORS_ORIGINS")
    
    @field_validator('cors_origins', mode='before')
    @classmethod
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v
    
    model_config = {
        "env_prefix": "SECURITY_",
        "extra": "ignore"
    }


class LoggingSettings(BaseSettings):
    """日志配置"""
    
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    
    # 文件日志
    file_enabled: bool = Field(default=True, env="LOG_FILE_ENABLED")
    file_path: str = Field(default="logs/app.log", env="LOG_FILE_PATH")
    file_max_size: str = Field(default="10MB", env="LOG_FILE_MAX_SIZE")
    file_backup_count: int = Field(default=5, env="LOG_FILE_BACKUP_COUNT")
    
    # 控制台日志
    console_enabled: bool = Field(default=True, env="LOG_CONSOLE_ENABLED")
    
    model_config = {
        "env_prefix": "LOG_",
        "extra": "ignore"
    }


class RedisSettings(BaseSettings):
    """Redis缓存配置"""
    
    host: str = Field(default="localhost", env="REDIS_HOST")
    port: int = Field(default=6379, env="REDIS_PORT")
    password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    database: int = Field(default=0, env="REDIS_DB")
    
    # 连接池配置
    max_connections: int = Field(default=20, env="REDIS_MAX_CONNECTIONS")
    connection_timeout: int = Field(default=5, env="REDIS_TIMEOUT")
    
    # 缓存配置
    default_ttl: int = Field(default=3600, env="REDIS_DEFAULT_TTL")
    
    @property
    def redis_url(self) -> str:
        """构建Redis连接URL"""
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.database}"
        return f"redis://{self.host}:{self.port}/{self.database}"
    
    model_config = {
        "env_prefix": "REDIS_",
        "extra": "ignore"
    }


class Settings(BaseSettings):
    """主配置类"""
    
    # 应用基础配置
    app_name: str = Field(default="AI Trading System", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    
    # 服务配置
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    
    # 子配置
    database: DatabaseSettings = DatabaseSettings()
    ai_models: AIModelSettings = AIModelSettings()
    qmt: QMTSettings = QMTSettings()
    trading: TradingSettings = TradingSettings()
    security: SecuritySettings = SecuritySettings()
    logging: LoggingSettings = LoggingSettings()
    redis: RedisSettings = RedisSettings()
    
    # 配置文件路径
    config_dir: str = Field(default="config", env="CONFIG_DIR")
    
    model_config = {
        "env_file": "infrastructure/.env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore"
    }
    
    def load_yaml_configs(self) -> None:
        """加载YAML配置文件"""
        config_path = Path(self.config_dir)
        if not config_path.exists():
            return
        
        # 加载各种配置文件
        config_files = {
            "ai_models.yaml": self.ai_models,
            "trading.yaml": self.trading,
            "security.yaml": self.security,
        }
        
        for filename, config_obj in config_files.items():
            file_path = config_path / filename
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    yaml_data = yaml.safe_load(f)
                    if yaml_data:
                        # 更新配置对象
                        for key, value in yaml_data.items():
                            if hasattr(config_obj, key):
                                setattr(config_obj, key, value)
    
    @model_validator(mode='after')
    def validate_settings(self):
        """配置验证"""
        # 在生产环境中验证必需的敏感信息
        # 开发环境可以使用默认值
        return self
